<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/stylesheet/style.css"> <!-- Link your custom CSS -->
</head>
<body>
  <!-- Navbar -->
  <%- include('partials/navbar') %>
  <main class="container mt-5">
    <h1 class="text-center">Payment</h1>
    <div class="mt-4">
      <h3>Order Summary</h3>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Item</th>
            <th>Description</th>
            <th>Price</th>
            <th>Quantity</th>
          </tr>
        </thead>
        <tbody>
          <% let totalAmount = 0; %>
          <% cart.forEach(item => { %>
            <tr>
              <td><%= item.gear_name %></td>
              <td><%= item.gear_desc %></td>
              <td>$<%= item.price_per_unit %></td>
              <td><%= item.quantity || 1 %></td>
            </tr>
            <% totalAmount += parseFloat(item.price_per_unit) * (item.quantity || 1); %>
          <% }) %>
        </tbody>
      </table>
      <div class="text-end">
        <h4>Total: $<%= totalAmount.toFixed(2) %></h4>
      </div>
    </div>
    <div class="mt-4">
      <h3>Customer Details</h3>
      <p><strong>Name:</strong> <%= customer.name %></p>
      <p><strong>Email:</strong> <%= customer.email %></p>
      <p><strong>Address:</strong> <%= customer.address %></p>
    </div>
    <div class="text-end mt-4">
      <form action="/payment/process" method="POST">
        <button type="submit" class="btn btn-success">Pay Now</button>
      </form>
    </div>
  </main>
</body>
</html>