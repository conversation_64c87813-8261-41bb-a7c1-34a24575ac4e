<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Matches - Football Club</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <link rel="stylesheet" href="/stylesheet/style.css" />
</head>
<body>
  <!-- Navbar -->
  <%- include('partials/navbar') %>

  <main class="container-fluid p-0">
    <!-- Red Header Section -->
    <div class="matches-header">
      <div class="container">
        <h1 class="matches-title">Matches</h1>

        <!-- Tab Navigation -->
        <div class="matches-tabs">
          <button class="tab-btn active" onclick="showTab('results')">Results</button>
          <button class="tab-btn" onclick="showTab('tickets')">Tickets</button>
        </div>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="container mt-4">
      <!-- Results Tab Content -->
      <div id="results-tab" class="tab-content active">
        <div class="content-placeholder">
          <p class="text-center text-muted">Results content will be displayed here</p>
          <!-- Future: Match results, scores, league tables will go here -->
        </div>
      </div>

      <!-- Tickets Tab Content -->
      <div id="tickets-tab" class="tab-content">
        <div class="content-placeholder">
          <p class="text-center text-muted">Tickets content will be displayed here</p>
          <!-- Future: Ticket booking, pricing, seat selection will go here -->
        </div>
      </div>
    </div>
  </main>

  <!-- JavaScript for Tab Functionality -->
  <script>
    // Simple tab switching function
    function showTab(tabName) {
      // Hide all tab contents
      const allTabs = document.querySelectorAll('.tab-content');
      allTabs.forEach(tab => tab.classList.remove('active'));

      // Remove active class from all tab buttons
      const allButtons = document.querySelectorAll('.tab-btn');
      allButtons.forEach(btn => btn.classList.remove('active'));

      // Show selected tab content
      document.getElementById(tabName + '-tab').classList.add('active');

      // Add active class to clicked button
      event.target.classList.add('active');
    }
  </script>

  <%- include('partials/footer') %>
</body>
</html>
