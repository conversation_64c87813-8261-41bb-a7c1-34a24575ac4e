<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Football Club Home</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <link rel="stylesheet" href="/stylesheet/style.css" />
</head>
<body>
  <!-- Navbar -->
  <%- include('partials/navbar') %>

  <main class="container mt-4">
    <!-- Blue Lock Club Section (Slideshow placeholder) -->
    <section class="bluelock-section mb-4">
      <h1 class="text-center mb-4">Blue Lock Club</h1>
      <div class="slideshow-container">
        <div class="slideshow-placeholder">
          <p class="text-center text-muted">Slideshow of character pics</p>
          <!-- Slideshow dots indicator -->
          <div class="slideshow-dots text-center mt-3">
            <span class="dot active"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
    </section>

    <!-- Shop Section -->
    <section class="shop-section mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>SHOP NOW</h2>
        <a href="/store" class="text-decoration-none">
          <span class="text-primary">View Store</span>
        </a>
      </div>

      <!-- Shop items with scroll arrows -->
      <div class="shop-scroll-wrapper position-relative">
        <!-- Left Arrow -->
        <button class="shop-arrow shop-arrow-left" onclick="scrollShopLeft()">
          <i class="fas fa-chevron-left"></i>
        </button>

        <!-- Shop items container -->
        <div class="shop-scroll-container" id="shopContainer">
          <div class="shop-scroll-items">
            <!-- Shop Item 1 -->
            <div class="shop-item">
              <div class="shop-item-image">
                <span>item</span>
              </div>
              <h6 class="mt-2">Jersey</h6>
              <p class="fw-bold">$146.00</p>
              <button class="btn btn-danger btn-sm w-100">BUY NOW</button>
            </div>

            <!-- Shop Item 2 -->
            <div class="shop-item">
              <div class="shop-item-image">
                <span>item</span>
              </div>
              <h6 class="mt-2">Boots</h6>
              <p class="fw-bold">$111.00</p>
              <button class="btn btn-danger btn-sm w-100">BUY NOW</button>
            </div>

            <!-- Shop Item 3 -->
            <div class="shop-item">
              <div class="shop-item-image">
                <span>item</span>
              </div>
              <h6 class="mt-2">Training Ball</h6>
              <p class="fw-bold">$25.00</p>
              <button class="btn btn-danger btn-sm w-100">BUY NOW</button>
            </div>

            <!-- Shop Item 4 -->
            <div class="shop-item">
              <div class="shop-item-image">
                <span>item</span>
              </div>
              <h6 class="mt-2">Scarf</h6>
              <p class="fw-bold">$18.00</p>
              <button class="btn btn-danger btn-sm w-100">BUY NOW</button>
            </div>
          </div>
        </div>

        <!-- Right Arrow -->
        <button class="shop-arrow shop-arrow-right" onclick="scrollShopRight()">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </section>

    <!-- Upcoming Matches Section -->
    <section class="matches-section mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>UPCOMING MATCHES</h2>
        <div class="countdown">
          <span id="days">00</span> DAYS
          <span id="hours">00</span> HOURS
          <span id="mins">00</span> MINS
          <span id="secs">00</span> SECS
        </div>
        <a href="#" class="text-decoration-none">
          <i class="fas fa-sync"></i> Sync to calendar
        </a>
      </div>

      <div class="match-scroll-container">
        <div class="match-scroll-wrapper">
          <!-- Previous Games -->
          <div class="match-card previous-game">
            <h4>PREVIOUS GAMES</h4>
            <a href="/matches" class="text-white">See all the results</a>
          </div>

          <!-- Match 1 -->
          <div class="match-card">
            <div class="d-flex justify-content-between mb-2">
              <div class="score">3</div>
              <div><i class="fas fa-trophy text-warning"></i></div>
              <div class="score">2</div>
            </div>
            <h5>Barcelona vs Real Madrid</h5>
            <p class="mb-1">Sunday, April 27, 14:00 CET</p>
            <p class="mb-1">Copa Del Rey, Estadio Olímpico de la Cartuja</p>
            <a href="#" class="btn btn-sm btn-outline-primary">Match Center</a> <!-- Direct user to stadium location -->
          </div>

          <!-- Match 2 -->
          <div class="match-card">
            <div class="d-flex justify-content-between mb-2">
              <div class="score">3</div>
              <div><i class="fas fa-trophy text-warning"></i></div>
              <div class="score">3</div>
            </div>
            <h5>Girona vs Mallorca</h5>
            <p class="mb-1">Thursday, May 1, 3:00 CET</p>
            <p class="mb-1">UEFA Champions League, Estadi Olímpic Lluís Companys</p>
            <a href="#" class="btn btn-sm btn-outline-primary">Match Center</a>
          </div>

          <!-- Next Game -->
          <div class="match-card next-game">
            <h4>NEXT GAME</h4>
            <a href="/matches" class="text-white">See all the results</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Trending Section -->
    <section class="trending-section mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>TRENDING</h2>
        <a href="/trending" class="text-decoration-none">
        </a>
      </div>
      <div class="trending-scroll-container">
        <div class="trending-scroll-wrapper">
          <div class="trending-item">
            <span class="trending-badge badge-live">LIVE</span>
            <div class="position-absolute bottom-0 p-2">
              <h5>Watch U18s Football</h5>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-live">LIVE</span>
            <div class="position-absolute bottom-0 p-2">
              <h5>Friendly Games U20</h5>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h5>Rising Stars</h5>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h5>Exclusive Clips</h5>
            </div>
          </div>
          <div class="trending-item">
            <span class="trending-badge badge-new">NEW</span>
            <div class="position-absolute bottom-0 p-2">
              <h5>HIGHLIGHTS | FINALS</h5>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="news-section mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>NEWS</h2>
      </div>

      <!-- Featured News Article -->
      <div class="featured-news-card mb-3">
        <div class="featured-news-content">
          <h3>Premier League Golden Awards</h3>
          <p>Who will win the Premier League Golden Awards?</p>
        </div>
      </div>

      <!-- Secondary News Articles -->
      <div class="row">
        <!-- News Item 1 -->
        <div class="col-md-4 mb-3">
          <div class="secondary-news-card">
            <div class="secondary-news-content">
              <h6>A1</h6>
            </div>
          </div>
        </div>

        <!-- News Item 2 -->
        <div class="col-md-4 mb-3">
          <div class="secondary-news-card">
            <div class="secondary-news-content">
              <h6>A2</h6>
            </div>
          </div>
        </div>

        <!-- News Item 3 -->
        <div class="col-md-4 mb-3">
          <div class="secondary-news-card">
            <div class="secondary-news-content">
              <h6>A3</h6>
            </div>
          </div>
        </div>
      </div>

      <!-- See More Button -->
      <div class="text-center mt-3">
        <a href="/news" class="btn btn-danger">View All News</a>
      </div>
    </section>
  </main>

<!-- Javascript Functions -->
  <!-- Countdown Timer Script -->
  <script>
    // Set the date we're counting down to (next match date)
    const countDownDate = new Date("May 1, 2025 15:00:00").getTime();

    // Update the countdown every 1 second
    const x = setInterval(function() {
      // Get today's date and time
      const now = new Date().getTime();

      // Find the distance between now and the count down date
      const distance = countDownDate - now;

      // Time calculations for days, hours, minutes and seconds
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      // Display the result
      document.getElementById("days").innerHTML = days.toString().padStart(2, '0');
      document.getElementById("hours").innerHTML = hours.toString().padStart(2, '0');
      document.getElementById("mins").innerHTML = minutes.toString().padStart(2, '0');
      document.getElementById("secs").innerHTML = seconds.toString().padStart(2, '0');

      // If the count down is finished, write some text
      if (distance < 0) {
        clearInterval(x);
        document.getElementById("countdown").innerHTML = "MATCH DAY!";
      }
    }, 1000);

    // Shop scroll functionality
    function scrollShopLeft() {
      const container = document.getElementById('shopContainer');
      container.scrollBy({
        left: -300, // Scroll left by 300px
        behavior: 'smooth'
      });
    }

    function scrollShopRight() {
      const container = document.getElementById('shopContainer');
      container.scrollBy({
        left: 300, // Scroll right by 300px
        behavior: 'smooth'
      });
    }
  </script>

  <%- include('partials/footer') %>
</body>
</html>
